'use client'

import { useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import { actions } from '@/common/utils/mixpanel';

// Global tracker to prevent multiple instances from tracking the same page
const globalTracker = {
  lastTrackedPath: '',
  isTracking: false,
};

export const GlobalPageViewTracker = () => {
  const pathname = usePathname();
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Prevent duplicate tracking for the same path
    if (globalTracker.lastTrackedPath === pathname || globalTracker.isTracking) {
      return;
    }

    globalTracker.isTracking = true;

    // Add a delay to ensure this runs only once per navigation
    timeoutRef.current = setTimeout(() => {
      // Double-check we haven't already tracked this path
      if (globalTracker.lastTrackedPath !== pathname) {
        actions.track('Page View', {
          page: pathname,
          path: pathname,
          timestamp: Date.now(),
        });
        
        globalTracker.lastTrackedPath = pathname;
      }
      globalTracker.isTracking = false;
    }, 150);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      globalTracker.isTracking = false;
    };
  }, [pathname]);

  return null;
};
