'use client'

import { useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import { actions } from '@/common/utils/mixpanel';

interface PageViewTrackerProps {
  pageName?: string;
  properties?: Record<string, unknown>;
}

export const PageViewTracker = ({
  pageName, properties = {},
}: PageViewTrackerProps) => {
  const pathname = usePathname();
  const hasTracked = useRef(false);
  const lastTrackedPath = useRef<string>('');

  useEffect(() => {
    const pageTitle = pageName || pathname;
    const currentPath = `${pathname}-${pageTitle}`;

    // Prevent duplicate tracking for the same page
    if (hasTracked.current && lastTrackedPath.current === currentPath) {
      return;
    }

    // Add a small delay to ensure this runs only once per page load
    const timeoutId = setTimeout(() => {
      actions.track('Page View', {
        page: pageTitle,
        path: pathname,
        ...properties,
      });

      hasTracked.current = true;
      lastTrackedPath.current = currentPath;
    }, 100);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [pathname, pageName]); // Removed properties from dependencies to prevent unnecessary re-renders

  return null;
};
